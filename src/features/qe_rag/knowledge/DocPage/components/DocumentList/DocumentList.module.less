// 基础布局样式
.documentListCard {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  :global(.ant-card-head) {
    padding: 16px 16px 8px 16px;
  }

  :global(.ant-card-body) {
    padding: 8px 16px 16px 16px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
}

.listMainContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.listScrollContainer {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

// 下拉菜单项样式
.dropdownItemDisable {
  color: #faad14;
}

.dropdownItemEnable {
  color: #52c41a;
}

.dropdownItemDelete {
  color: #ff4d4f;
}

.dropdownIconMargin {
  margin-right: 8px;
}

// 分页容器样式
.paginationContainer {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

// 文档ID标签样式
.documentIdTag {
  background-color: #f5f5f5;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

// 表头样式
.listHeader {
  display: flex;
  padding: 8px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  font-size: 14px;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.headerIdColumn {
  width: 40px;
  text-align: center;
}

.headerNameColumn {
  flex: 1;
}

.headerTypeColumn {
  width: 60px;
  text-align: center;
}

.headerStatusColumn {
  width: 80px;
  text-align: center;
}

.headerActionColumn {
  width: 40px;
}

// 文档列表项样式
.documentItem {
  cursor: pointer !important;
  background-color: transparent !important;
  color: #333333 !important;
  padding: 5px 20px !important;
  border-radius: 8px !important;
  margin-bottom: 4px !important;
  border: none !important;
  transition: all 0.2s ease !important;
  box-shadow: none !important;

  &:hover {
    background-color: #f5f5f5 !important;
  }
}

.documentItemSelected {
  cursor: pointer !important;
  background-color: #1677ff !important;
  color: #ffffff !important;
  padding: 5px 20px !important;
  border-radius: 8px !important;
  margin-bottom: 4px !important;
  border: none !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3) !important;

  &:hover {
    background-color: #1677ff !important;
  }
}

// 列表项内容布局
.itemContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.itemMainContent {
  flex: 1;
}

// 文档标题样式
.documentTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}

.documentTitleNormal {
  color: #333333;
  font-weight: 400;
}

.documentTitleSelected {
  color: #ffffff;
  font-weight: 500;
}

// 右侧操作区域
.itemActions {
  display: flex;
  align-items: center;
  gap: 8px;
}

// 类型标签样式
.typeTag {
  min-width: 50px;
  text-align: center;
  display: inline-block;
}

// 下拉菜单触发器
.dropdownTrigger {
  cursor: pointer;
  padding: 4px;
}

.moreIconNormal {
  color: #999999;
}

.moreIconSelected {
  color: #ffffff;
}
